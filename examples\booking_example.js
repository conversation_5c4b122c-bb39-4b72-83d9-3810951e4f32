// Contoh penggunaan API Booking System
// Pastikan server sudah running dan Anda sudah memiliki token authentication

const API_BASE_URL = 'http://localhost:3000';
const AUTH_TOKEN = 'your_auth_token_here'; // Ganti dengan token yang valid

// Helper function untuk HTTP requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const options = {
    method,
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'Content-Type': 'application/json'
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    console.log(`${method} ${endpoint}:`);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(result, null, 2));
    console.log('---\n');
    
    return result;
  } catch (error) {
    console.error(`Error ${method} ${endpoint}:`, error);
    return null;
  }
}

// Contoh penggunaan
async function demonstrateBookingSystem() {
  console.log('=== DEMO BOOKING SYSTEM ===\n');

  // 1. Buat booking baru
  console.log('1. Membuat booking baru...');
  const newBooking = await apiRequest('/booking', 'POST', {
    nama: 'John Doe',
    nomortlp: '081234567890',
    tanggal_book: '2025-08-03 10:00:00 UTC',
    hotel: 'hotel_id_here', // Ganti dengan ID hotel yang valid
    transport: 'Yes',
    transport_type: 'Medium Car',
    nomor_driver: ''
  });

  if (!newBooking || !newBooking.data) {
    console.log('Gagal membuat booking. Pastikan data valid dan token benar.');
    return;
  }

  const bookingId = newBooking.data.id;
  console.log(`Booking berhasil dibuat dengan ID: ${bookingId}\n`);

  // 2. Tambah aktivitas ke booking
  console.log('2. Menambah aktivitas pertama ke booking...');
  await apiRequest(`/booking/${bookingId}/activities`, 'POST', {
    activity: 'product_id_1', // Ganti dengan ID produk yang valid
    qty: 2
  });

  console.log('3. Menambah aktivitas kedua ke booking...');
  await apiRequest(`/booking/${bookingId}/activities`, 'POST', {
    activity: 'product_id_2', // Ganti dengan ID produk yang valid
    qty: 1
  });

  console.log('4. Menambah aktivitas ketiga ke booking...');
  const thirdActivity = await apiRequest(`/booking/${bookingId}/activities`, 'POST', {
    activity: 'product_id_3', // Ganti dengan ID produk yang valid
    qty: 3
  });

  // 5. Lihat semua aktivitas booking
  console.log('5. Melihat semua aktivitas booking...');
  await apiRequest(`/booking/${bookingId}/activities`);

  // 6. Update aktivitas (jika berhasil dibuat)
  if (thirdActivity && thirdActivity.data) {
    console.log('6. Mengupdate aktivitas ketiga...');
    await apiRequest(`/booking/activities/${thirdActivity.data.id}`, 'PUT', {
      qty: 5
    });
  }

  // 7. Lihat detail booking
  console.log('7. Melihat detail booking...');
  await apiRequest(`/booking/${bookingId}`);

  // 8. Lihat semua booking user
  console.log('8. Melihat semua booking user...');
  await apiRequest('/booking');

  // 9. Hapus aktivitas (opsional - uncomment jika ingin test)
  /*
  if (thirdActivity && thirdActivity.data) {
    console.log('9. Menghapus aktivitas ketiga...');
    await apiRequest(`/booking/activities/${thirdActivity.data.id}`, 'DELETE');
  }
  */

  console.log('=== DEMO SELESAI ===');
}

// Jalankan demo jika file ini dieksekusi langsung
if (require.main === module) {
  // Pastikan fetch tersedia (untuk Node.js versi lama)
  if (typeof fetch === 'undefined') {
    console.log('Untuk menjalankan contoh ini, install node-fetch:');
    console.log('npm install node-fetch');
    console.log('Atau gunakan Node.js versi 18+ yang sudah include fetch');
    process.exit(1);
  }

  demonstrateBookingSystem().catch(console.error);
}

module.exports = {
  apiRequest,
  demonstrateBookingSystem
};
