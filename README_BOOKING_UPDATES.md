# Booking System Updates

## <PERSON><PERSON><PERSON>an

Sistem booking telah diperbaiki dan ditingkatkan dengan fitur-fitur berikut:

### 1. Booking ID Random Namun Teratur
- **Format**: `BK-YYYYMMDD-XXXXX`
- **Contoh**: `BK-20250803-A1B2C`
- **<PERSON><PERSON>lasan**:
  - `BK` = Prefix untuk booking
  - `YYYYMMDD` = Tanggal booking dibuat
  - `XXXXX` = 5 karakter random alphanumeric
- **Keunikan**: Sistem memastikan ID tidak duplikat dengan melakukan pengecekan sebelum menyimpan

### 2. Kode Aktivitas Berurutan
- **Format**: `act001`, `act002`, `act003`, dst.
- **Scope**: Berurutan per booking (setiap booking mulai dari act001)
- **Auto-increment**: Otomatis menentukan nomor urut berikutnya

### 3. Collection Booking Activities
- **Nama Collection**: `booking_activities`
- **Fields**:
  - `id` (Text, Nonempty) - ID record
  - `code_activity` (Text) - Kode aktivitas (act001, act002, dst)
  - `booking_id` (Relation, Single) - Relasi ke booking_uab
  - `activity` (Relation, Single) - Relasi ke product_uab
  - `qty` (Number) - Jumlah/kuantitas aktivitas
  - `created` (Date, Create) - Tanggal dibuat
  - `updated` (Date, Create/Update) - Tanggal diupdate

## File-File yang Dibuat/Dimodifikasi

### File Baru:
1. **`utils/idGenerator.js`** - Utility untuk generate ID dan kode
2. **`controllers/bookingActivityController.js`** - Controller untuk aktivitas booking
3. **`routes/bookingActivityRoutes.js`** - Routes untuk aktivitas booking
4. **`API_DOCUMENTATION.md`** - Dokumentasi API lengkap
5. **`examples/booking_example.js`** - Contoh penggunaan API
6. **`README_BOOKING_UPDATES.md`** - File ini

### File yang Dimodifikasi:
1. **`controllers/bookingController.js`** - Ditambah generate booking ID dan endpoint baru
2. **`routes/bookingRoutes.js`** - Ditambah endpoint untuk get bookings
3. **`server.js`** - Ditambah routes untuk booking activities

## Struktur API Endpoints

### Booking Endpoints:
- `POST /booking` - Buat booking baru (dengan ID random)
- `GET /booking` - Get semua booking user
- `GET /booking/:bookingId` - Get detail booking

### Booking Activities Endpoints:
- `POST /booking/:bookingId/activities` - Tambah aktivitas ke booking
- `GET /booking/:bookingId/activities` - Get semua aktivitas booking
- `PUT /booking/activities/:activityId` - Update aktivitas
- `DELETE /booking/activities/:activityId` - Hapus aktivitas

## Cara Penggunaan

### 1. Setup Collection di PocketBase
Pastikan collection `booking_activities` sudah dibuat dengan struktur:
```
- id (Text, Nonempty)
- code_activity (Text)
- booking_id (Relation to booking_uab, Single)
- activity (Relation to product_uab, Single)  
- qty (Number)
- created (Date, Create)
- updated (Date, Create/Update)
```

### 2. Contoh Flow Penggunaan:

#### A. Buat Booking Baru
```javascript
POST /booking
{
  "nama": "John Doe",
  "nomortlp": "081234567890",
  "tanggal_book": "2025-08-03 10:00:00 UTC",
  "hotel": "hotel_id_here",
  "transport": "Yes",
  "transport_type": "Medium Car"
}

// Response: booking dengan ID seperti BK-20250803-A1B2C
```

#### B. Tambah Aktivitas ke Booking
```javascript
POST /booking/BK-20250803-A1B2C/activities
{
  "activity": "product_id_1",
  "qty": 2
}
// Response: aktivitas dengan code_activity "act001"

POST /booking/BK-20250803-A1B2C/activities
{
  "activity": "product_id_2", 
  "qty": 1
}
// Response: aktivitas dengan code_activity "act002"
```

#### C. Lihat Aktivitas Booking
```javascript
GET /booking/BK-20250803-A1B2C/activities
// Response: array aktivitas dengan kode act001, act002, dst
```

### 3. Testing
Gunakan file `examples/booking_example.js` untuk testing:
```bash
# Install dependencies jika belum
npm install node-fetch  # untuk Node.js < 18

# Edit file dan ganti AUTH_TOKEN dengan token valid
# Jalankan contoh
node examples/booking_example.js
```

## Keamanan dan Validasi

### 1. Authentication
- Semua endpoint memerlukan token authentication
- User hanya bisa akses booking milik mereka sendiri

### 2. Validasi Input
- Booking: validasi transportasi dan nomor driver
- Activities: validasi activity ID dan qty > 0
- ID uniqueness: sistem memastikan booking ID tidak duplikat

### 3. Error Handling
- Proper HTTP status codes
- Descriptive error messages
- Logging untuk debugging

## Manfaat Perubahan

### 1. Booking ID yang Terstruktur
- **Mudah dibaca**: Format yang konsisten dan informatif
- **Traceable**: Bisa langsung tahu tanggal booking dari ID
- **Unique**: Sistem memastikan tidak ada duplikat
- **Professional**: Terlihat lebih profesional untuk customer

### 2. Kode Aktivitas Berurutan
- **Organized**: Mudah tracking aktivitas dalam satu booking
- **Sequential**: Urutan yang jelas untuk setiap aktivitas
- **Scalable**: Bisa menampung banyak aktivitas per booking
- **User-friendly**: Mudah dipahami oleh user dan admin

### 3. Sistem yang Lebih Robust
- **Modular**: Code terorganisir dalam file-file terpisah
- **Maintainable**: Mudah untuk maintenance dan pengembangan
- **Documented**: API terdokumentasi dengan baik
- **Testable**: Dilengkapi contoh penggunaan untuk testing

## Next Steps (Opsional)

1. **Notification System**: Kirim notifikasi saat booking dibuat/diupdate
2. **Payment Integration**: Integrasikan dengan sistem pembayaran
3. **Booking Status**: Tambah status booking (pending, confirmed, cancelled)
4. **Reporting**: Dashboard untuk melihat statistik booking
5. **Email Confirmation**: Kirim email konfirmasi booking ke customer
